% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
\documentclass[
]{article}
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath,amssymb}
\setcounter{secnumdepth}{5}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{248,248,248}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.94,0.16,0.16}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\BuiltInTok}[1]{#1}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.64,0.00,0.00}{\textbf{#1}}}
\newcommand{\ExtensionTok}[1]{#1}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\ImportTok}[1]{#1}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{#1}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\RegionMarkerTok}[1]{#1}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.00,0.00,0.00}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\usepackage{longtable,booktabs,array}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\usepackage{float}
\floatplacement{figure}{H}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  pdftitle={Social Media Analytics Assignment: Mastodon Data Analysis},
  pdfauthor={Western Sydney University MBA Student},
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\title{Social Media Analytics Assignment: Mastodon Data Analysis}
\usepackage{etoolbox}
\makeatletter
\providecommand{\subtitle}[1]{% add subtitle to \maketitle
  \apptocmd{\@title}{\par {\large #1 \par}}{}{}
}
\makeatother
\subtitle{Using the rtoot Package for R}
\author{Western Sydney University MBA Student}
\date{2025-05-25}

\begin{document}
\maketitle

{
\setcounter{tocdepth}{3}
\tableofcontents
}
\section{Executive Summary}\label{executive-summary}

This assignment analyzes social media data from Mastodon, a
decentralized social media platform, using the \texttt{rtoot} package in
R. The analysis covers instance activity patterns, content engagement
metrics, hashtag trends, and comparative analysis between general posts
and technical community content (\#rstats). Key findings reveal distinct
engagement patterns in decentralized social media compared to
traditional platforms.

\section{Introduction}\label{introduction}

\subsection{Background}\label{background}

Mastodon represents a new paradigm in social media - a decentralized,
federated network where users can join different instances (servers)
while still communicating across the entire network. This structure
provides unique opportunities for social media analytics, particularly
in understanding community-specific behaviors and engagement patterns.

\subsection{Objectives}\label{objectives}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  Analyze instance-level activity patterns on mastodon.social
\item
  Examine content engagement metrics and posting behaviors
\item
  Investigate hashtag trends and their usage patterns
\item
  Compare engagement between general content and technical communities
\item
  Perform text analysis on specialized content (\#rstats posts)
\item
  Provide insights for social media strategy in decentralized platforms
\end{enumerate}

\subsection{Methodology}\label{methodology}

This analysis uses the \texttt{rtoot} package, which provides R
interface to the Mastodon API. Data collection includes: - Instance
activity metrics (3-month historical data) - Public timeline posts
(sample of 100 posts) - Hashtag-specific content (\#rstats, sample of
100 posts) - Trending hashtags and engagement metrics

\section{Data Collection and Setup}\label{data-collection-and-setup}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Install and load required packages}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(rtoot)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"rtoot"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(dplyr)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"dplyr"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(ggplot2)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"ggplot2"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(lubridate)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"lubridate"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(tidytext)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"tidytext"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(wordcloud)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"wordcloud"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(knitr)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"knitr"}\NormalTok{)}
\NormalTok{\}}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{require}\NormalTok{(tidyr)) \{}
  \FunctionTok{install.packages}\NormalTok{(}\StringTok{"tidyr"}\NormalTok{)}
\NormalTok{\}}

\FunctionTok{library}\NormalTok{(rtoot)}
\FunctionTok{library}\NormalTok{(dplyr)}
\FunctionTok{library}\NormalTok{(ggplot2)}
\FunctionTok{library}\NormalTok{(lubridate)}
\FunctionTok{library}\NormalTok{(tidytext)}
\FunctionTok{library}\NormalTok{(wordcloud)}
\FunctionTok{library}\NormalTok{(knitr)}
\FunctionTok{library}\NormalTok{(tidyr)}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Check if authentication already exists, if not, set up new authentication}
\NormalTok{token\_path }\OtherTok{\textless{}{-}} \FunctionTok{file.path}\NormalTok{(tools}\SpecialCharTok{::}\FunctionTok{R\_user\_dir}\NormalTok{(}\StringTok{"rtoot"}\NormalTok{, }\StringTok{"config"}\NormalTok{), }\StringTok{"account1.rds"}\NormalTok{)}

\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{file.exists}\NormalTok{(token\_path)) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"Setting up new authentication...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \CommentTok{\# This will open browser for authentication}
  \FunctionTok{auth\_setup}\NormalTok{(}\AttributeTok{name =} \StringTok{"account1"}\NormalTok{)}
\NormalTok{\} }\ControlFlowTok{else}\NormalTok{ \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"Using existing authentication...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Using existing authentication...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Load authentication token}
\NormalTok{token }\OtherTok{\textless{}{-}} \FunctionTok{readRDS}\NormalTok{(token\_path)}
\FunctionTok{options}\NormalTok{(}\StringTok{"rtoot\_token"} \OtherTok{=}\NormalTok{ token\_path)}

\FunctionTok{cat}\NormalTok{(}\StringTok{"Authentication successful! Token loaded from:"}\NormalTok{, token\_path, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Authentication successful! Token loaded from: /Users/<USER>/Library/Preferences/org.R-project.R/R/rtoot/account1.rds
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Token file exists:"}\NormalTok{, }\FunctionTok{file.exists}\NormalTok{(token\_path), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Token file exists: TRUE
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Real data collection from Mastodon API using authenticated account}
\CommentTok{\# This collects actual data from mastodon.social}

\FunctionTok{cat}\NormalTok{(}\StringTok{"Collecting data from Mastodon API...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Collecting data from Mastodon API...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Instance information and activity}
\FunctionTok{cat}\NormalTok{(}\StringTok{"Getting instance information...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Getting instance information...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{tryCatch}\NormalTok{(\{}
\NormalTok{  instance\_info }\OtherTok{\textless{}{-}} \FunctionTok{get\_instance\_general}\NormalTok{(}\AttributeTok{instance =} \StringTok{"mastodon.social"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✓ Instance info retrieved successfully}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  Instance title:"}\NormalTok{, instance\_info}\SpecialCharTok{$}\NormalTok{title, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  User count:"}\NormalTok{, }\FunctionTok{format}\NormalTok{(instance\_info}\SpecialCharTok{$}\NormalTok{stats}\SpecialCharTok{$}\NormalTok{user\_count, }\AttributeTok{big.mark =} \StringTok{","}\NormalTok{), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}, }\AttributeTok{error =} \ControlFlowTok{function}\NormalTok{(e) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✗ Instance info failed:"}\NormalTok{, e}\SpecialCharTok{$}\NormalTok{message, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{  instance\_info }\OtherTok{\textless{}\textless{}{-}} \ConstantTok{NULL}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## ✓ Instance info retrieved successfully
##   Instance title: Mastodon 
##   User count: 2,743,423
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Getting instance activity (last 3 months)...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Getting instance activity (last 3 months)...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{tryCatch}\NormalTok{(\{}
\NormalTok{  instance\_activity }\OtherTok{\textless{}{-}} \FunctionTok{get\_instance\_activity}\NormalTok{(}\AttributeTok{instance =} \StringTok{"mastodon.social"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✓ Instance activity retrieved successfully}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  Records:"}\NormalTok{, }\FunctionTok{nrow}\NormalTok{(instance\_activity), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}, }\AttributeTok{error =} \ControlFlowTok{function}\NormalTok{(e) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✗ Instance activity failed:"}\NormalTok{, e}\SpecialCharTok{$}\NormalTok{message, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{  instance\_activity }\OtherTok{\textless{}\textless{}{-}} \FunctionTok{data.frame}\NormalTok{()}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## ✓ Instance activity retrieved successfully
##   Records: 12
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Getting trending hashtags...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Getting trending hashtags...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{tryCatch}\NormalTok{(\{}
\NormalTok{  instance\_trends }\OtherTok{\textless{}{-}} \FunctionTok{get\_instance\_trends}\NormalTok{(}\AttributeTok{instance =} \StringTok{"mastodon.social"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✓ Trending hashtags retrieved successfully}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  Number of trends:"}\NormalTok{, }\FunctionTok{nrow}\NormalTok{(instance\_trends), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}, }\AttributeTok{error =} \ControlFlowTok{function}\NormalTok{(e) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✗ Trending hashtags failed:"}\NormalTok{, e}\SpecialCharTok{$}\NormalTok{message, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{  instance\_trends }\OtherTok{\textless{}\textless{}{-}} \FunctionTok{data.frame}\NormalTok{()}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## ✓ Trending hashtags retrieved successfully
##   Number of trends: 70
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Content data}
\FunctionTok{cat}\NormalTok{(}\StringTok{"Getting public timeline (100 posts)...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Getting public timeline (100 posts)...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{tryCatch}\NormalTok{(\{}
\NormalTok{  public\_timeline }\OtherTok{\textless{}{-}} \FunctionTok{get\_timeline\_public}\NormalTok{(}\AttributeTok{instance =} \StringTok{"mastodon.social"}\NormalTok{,}
                                        \AttributeTok{limit =} \DecValTok{100}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✓ Public timeline retrieved successfully}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  Posts retrieved:"}\NormalTok{, }\FunctionTok{nrow}\NormalTok{(public\_timeline), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}, }\AttributeTok{error =} \ControlFlowTok{function}\NormalTok{(e) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✗ Public timeline failed:"}\NormalTok{, e}\SpecialCharTok{$}\NormalTok{message, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{  public\_timeline }\OtherTok{\textless{}\textless{}{-}} \FunctionTok{data.frame}\NormalTok{()}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
##   |                                                                              |                                                                      |   0%  |                                                                              |===================================                                   |  50%  |                                                                              |======================================================================| 100%
## ✓ Public timeline retrieved successfully
##   Posts retrieved: 120
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Getting \#rstats posts (100 posts)...}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Getting #rstats posts (100 posts)...
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{tryCatch}\NormalTok{(\{}
\NormalTok{  rstats\_posts }\OtherTok{\textless{}{-}} \FunctionTok{get\_timeline\_hashtag}\NormalTok{(}\AttributeTok{hashtag =} \StringTok{"rstats"}\NormalTok{,}
                                      \AttributeTok{instance =} \StringTok{"mastodon.social"}\NormalTok{,}
                                      \AttributeTok{limit =} \DecValTok{100}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✓ \#rstats posts retrieved successfully}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"  Posts retrieved:"}\NormalTok{, }\FunctionTok{nrow}\NormalTok{(rstats\_posts), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}, }\AttributeTok{error =} \ControlFlowTok{function}\NormalTok{(e) \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"✗ \#rstats posts failed:"}\NormalTok{, e}\SpecialCharTok{$}\NormalTok{message, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{  rstats\_posts }\OtherTok{\textless{}\textless{}{-}} \FunctionTok{data.frame}\NormalTok{()}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
##   |                                                                              |                                                                      |   0%  |                                                                              |===================================                                   |  50%  |                                                                              |======================================================================| 100%
## ✓ #rstats posts retrieved successfully
##   Posts retrieved: 120
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Display data collection summary}
\FunctionTok{cat}\NormalTok{(}\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{=== DATA COLLECTION SUMMARY ===}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## 
## === DATA COLLECTION SUMMARY ===
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Instance activity records:"}\NormalTok{, }\FunctionTok{ifelse}\NormalTok{(}\FunctionTok{exists}\NormalTok{(}\StringTok{"instance\_activity"}\NormalTok{),}
                                        \FunctionTok{nrow}\NormalTok{(instance\_activity), }\DecValTok{0}\NormalTok{), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Instance activity records: 12
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Trending hashtags:"}\NormalTok{, }\FunctionTok{ifelse}\NormalTok{(}\FunctionTok{exists}\NormalTok{(}\StringTok{"instance\_trends"}\NormalTok{),}
                                \FunctionTok{nrow}\NormalTok{(instance\_trends), }\DecValTok{0}\NormalTok{), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Trending hashtags: 70
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"Public timeline posts:"}\NormalTok{, }\FunctionTok{ifelse}\NormalTok{(}\FunctionTok{exists}\NormalTok{(}\StringTok{"public\_timeline"}\NormalTok{),}
                                    \FunctionTok{nrow}\NormalTok{(public\_timeline), }\DecValTok{0}\NormalTok{), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Public timeline posts: 120
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{cat}\NormalTok{(}\StringTok{"\#rstats posts:"}\NormalTok{, }\FunctionTok{ifelse}\NormalTok{(}\FunctionTok{exists}\NormalTok{(}\StringTok{"rstats\_posts"}\NormalTok{),}
                            \FunctionTok{nrow}\NormalTok{(rstats\_posts), }\DecValTok{0}\NormalTok{), }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## #rstats posts: 120
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Save data for backup/reuse}
\FunctionTok{save}\NormalTok{(instance\_info, instance\_activity, instance\_trends,}
\NormalTok{     public\_timeline, rstats\_posts,}
     \AttributeTok{file =} \StringTok{"mastodon\_data.RData"}\NormalTok{)}

\FunctionTok{cat}\NormalTok{(}\StringTok{"Data saved to mastodon\_data.RData}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
## Data saved to mastodon_data.RData
\end{verbatim}

\section{Instance Analysis}\label{instance-analysis}

\subsection{Activity Trends}\label{activity-trends}

The analysis of mastodon.social instance activity over the past 3 months
reveals consistent growth patterns:

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Check if we have instance activity data}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{exists}\NormalTok{(}\StringTok{"instance\_activity"}\NormalTok{) }\SpecialCharTok{\&\&} \FunctionTok{nrow}\NormalTok{(instance\_activity) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
  \CommentTok{\# Convert week to proper date format}
\NormalTok{  instance\_activity}\SpecialCharTok{$}\NormalTok{week }\OtherTok{\textless{}{-}} \FunctionTok{as.Date}\NormalTok{(instance\_activity}\SpecialCharTok{$}\NormalTok{week)}

  \CommentTok{\# Create activity trend plot}
\NormalTok{  activity\_plot }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(instance\_activity, }\FunctionTok{aes}\NormalTok{(}\AttributeTok{x =}\NormalTok{ week)) }\SpecialCharTok{+}
    \FunctionTok{geom\_line}\NormalTok{(}\FunctionTok{aes}\NormalTok{(}\AttributeTok{y =}\NormalTok{ statuses }\SpecialCharTok{/} \DecValTok{1000}\NormalTok{, }\AttributeTok{color =} \StringTok{"Posts (thousands)"}\NormalTok{),}
              \AttributeTok{size =} \FloatTok{1.2}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{geom\_line}\NormalTok{(}\FunctionTok{aes}\NormalTok{(}\AttributeTok{y =}\NormalTok{ logins }\SpecialCharTok{/} \DecValTok{1000}\NormalTok{, }\AttributeTok{color =} \StringTok{"Logins (thousands)"}\NormalTok{),}
              \AttributeTok{size =} \FloatTok{1.2}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{geom\_line}\NormalTok{(}\FunctionTok{aes}\NormalTok{(}\AttributeTok{y =}\NormalTok{ registrations, }\AttributeTok{color =} \StringTok{"New Registrations"}\NormalTok{),}
              \AttributeTok{size =} \FloatTok{1.2}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Mastodon.social Instance Activity Trends"}\NormalTok{,}
         \AttributeTok{subtitle =} \StringTok{"Weekly activity over the last 3 months"}\NormalTok{,}
         \AttributeTok{x =} \StringTok{"Week"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Count"}\NormalTok{,}
         \AttributeTok{color =} \StringTok{"Metric"}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{theme\_minimal}\NormalTok{() }\SpecialCharTok{+}
    \FunctionTok{theme}\NormalTok{(}\AttributeTok{legend.position =} \StringTok{"bottom"}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{scale\_color\_manual}\NormalTok{(}\AttributeTok{values =} \FunctionTok{c}\NormalTok{(}\StringTok{"Posts (thousands)"} \OtherTok{=} \StringTok{"blue"}\NormalTok{,}
                                 \StringTok{"Logins (thousands)"} \OtherTok{=} \StringTok{"green"}\NormalTok{,}
                                 \StringTok{"New Registrations"} \OtherTok{=} \StringTok{"red"}\NormalTok{))}

  \FunctionTok{print}\NormalTok{(activity\_plot)}

  \CommentTok{\# Summary statistics}
\NormalTok{  activity\_summary }\OtherTok{\textless{}{-}}\NormalTok{ instance\_activity }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{summarise}\NormalTok{(}
      \AttributeTok{avg\_weekly\_posts =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(statuses)),}
      \AttributeTok{avg\_weekly\_logins =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(logins)),}
      \AttributeTok{avg\_weekly\_registrations =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(registrations)),}
      \AttributeTok{total\_posts =} \FunctionTok{sum}\NormalTok{(statuses),}
      \AttributeTok{total\_registrations =} \FunctionTok{sum}\NormalTok{(registrations)}
\NormalTok{    )}

  \FunctionTok{kable}\NormalTok{(activity\_summary, }\AttributeTok{caption =} \StringTok{"Instance Activity Summary Statistics"}\NormalTok{)}
\NormalTok{\} }\ControlFlowTok{else}\NormalTok{ \{}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"No instance activity data available for analysis.}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}
\NormalTok{\}}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/activity_analysis-1} \end{center}

\begin{longtable}[]{@{}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1848}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1957}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2717}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1304}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2174}}@{}}
\caption{Instance Activity Summary Statistics}\tabularnewline
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_logins
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_registrations
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_registrations
\end{minipage} \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_logins
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_weekly\_registrations
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_registrations
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
897948 & 158213 & 13021 & 10775380 & 156255 \\
\end{longtable}

\subsection{Trending Hashtags
Analysis}\label{trending-hashtags-analysis}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Get top 10 trending hashtags}
\NormalTok{top\_trends }\OtherTok{\textless{}{-}}\NormalTok{ instance\_trends }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{arrange}\NormalTok{(}\FunctionTok{desc}\NormalTok{(uses)) }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{head}\NormalTok{(}\DecValTok{10}\NormalTok{)}

\CommentTok{\# Create hashtag popularity plot}
\NormalTok{trends\_plot }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(top\_trends, }\FunctionTok{aes}\NormalTok{(}\AttributeTok{x =} \FunctionTok{reorder}\NormalTok{(name, uses), }\AttributeTok{y =}\NormalTok{ uses)) }\SpecialCharTok{+}
  \FunctionTok{geom\_col}\NormalTok{(}\AttributeTok{fill =} \StringTok{"steelblue"}\NormalTok{, }\AttributeTok{alpha =} \FloatTok{0.7}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{coord\_flip}\NormalTok{() }\SpecialCharTok{+}
  \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Top 10 Trending Hashtags on Mastodon.social"}\NormalTok{,}
       \AttributeTok{x =} \StringTok{"Hashtag"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Total Uses"}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{theme\_minimal}\NormalTok{()}

\FunctionTok{print}\NormalTok{(trends\_plot)}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/hashtag_analysis-1} \end{center}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{kable}\NormalTok{(top\_trends, }\AttributeTok{caption =} \StringTok{"Top Trending Hashtags with Usage Statistics"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0744}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.2397}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.4793}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0909}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0744}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0413}}@{}}
\caption{Top Trending Hashtags with Usage Statistics}\tabularnewline
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
id
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
name
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
url
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
day
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
accounts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
uses
\end{minipage} \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
id
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
name
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
url
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
day
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
accounts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
uses
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
1362 & caturday & \url{https://mastodon.social/tags/caturday} &
2025-05-24 & 719 & 817 \\
******** & commercialcatchphrases &
\url{https://mastodon.social/tags/commercialcatchphrases} & 2025-05-25 &
72 & 324 \\
******** & fictionalcharacterslastwords &
\url{https://mastodon.social/tags/fictionalcharacterslastwords} &
2025-05-24 & 86 & 228 \\
353414 & SilentSunday & \url{https://mastodon.social/tags/silentsunday}
& 2025-05-25 & 153 & 162 \\
113963 & dfbpokalfinale &
\url{https://mastodon.social/tags/dfbpokalfinale} & 2025-05-24 & 35 &
137 \\
1362 & caturday & \url{https://mastodon.social/tags/caturday} &
2025-05-25 & 81 & 93 \\
4369 & towelday & \url{https://mastodon.social/tags/towelday} &
2025-05-25 & 69 & 74 \\
1362 & caturday & \url{https://mastodon.social/tags/caturday} &
2025-05-23 & 51 & 63 \\
353414 & SilentSunday & \url{https://mastodon.social/tags/silentsunday}
& 2025-05-19 & 50 & 56 \\
3834 & ScreenshotSaturday &
\url{https://mastodon.social/tags/ScreenshotSaturday} & 2025-05-24 & 54
& 56 \\
\end{longtable}

\section{Content Engagement Analysis}\label{content-engagement-analysis}

\subsection{Posting Patterns}\label{posting-patterns}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Analyze posting patterns by hour}
\NormalTok{public\_timeline}\SpecialCharTok{$}\NormalTok{hour }\OtherTok{\textless{}{-}} \FunctionTok{hour}\NormalTok{(public\_timeline}\SpecialCharTok{$}\NormalTok{created\_at)}

\NormalTok{hourly\_posts }\OtherTok{\textless{}{-}}\NormalTok{ public\_timeline }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{count}\NormalTok{(hour) }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{arrange}\NormalTok{(hour)}

\CommentTok{\# Create hourly posting pattern plot}
\NormalTok{hourly\_plot }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(hourly\_posts, }\FunctionTok{aes}\NormalTok{(}\AttributeTok{x =}\NormalTok{ hour, }\AttributeTok{y =}\NormalTok{ n)) }\SpecialCharTok{+}
  \FunctionTok{geom\_col}\NormalTok{(}\AttributeTok{fill =} \StringTok{"darkgreen"}\NormalTok{, }\AttributeTok{alpha =} \FloatTok{0.7}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Posting Patterns by Hour of Day"}\NormalTok{,}
       \AttributeTok{subtitle =} \StringTok{"Based on public timeline sample (n=100)"}\NormalTok{,}
       \AttributeTok{x =} \StringTok{"Hour of Day"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Number of Posts"}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{scale\_x\_continuous}\NormalTok{(}\AttributeTok{breaks =} \DecValTok{0}\SpecialCharTok{:}\DecValTok{23}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{theme\_minimal}\NormalTok{()}

\FunctionTok{print}\NormalTok{(hourly\_plot)}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/posting_patterns-1} \end{center}

\subsection{Engagement Metrics}\label{engagement-metrics}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Analyze engagement metrics}
\NormalTok{engagement\_summary }\OtherTok{\textless{}{-}}\NormalTok{ public\_timeline }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{summarise}\NormalTok{(}
    \AttributeTok{avg\_reblogs =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(reblogs\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
    \AttributeTok{avg\_favourites =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(favourites\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
    \AttributeTok{avg\_replies =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(replies\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
    \AttributeTok{total\_posts =} \FunctionTok{n}\NormalTok{(),}
    \AttributeTok{engagement\_rate =} \FunctionTok{round}\NormalTok{((avg\_reblogs }\SpecialCharTok{+}\NormalTok{ avg\_favourites }\SpecialCharTok{+}\NormalTok{ avg\_replies) }\SpecialCharTok{/} \DecValTok{3}\NormalTok{, }\DecValTok{2}\NormalTok{)}
\NormalTok{  )}

\FunctionTok{kable}\NormalTok{(engagement\_summary, }\AttributeTok{caption =} \StringTok{"Public Timeline Engagement Metrics"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{longtable}[]{@{}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1791}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2239}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1791}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1791}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2388}}@{}}
\caption{Public Timeline Engagement Metrics}\tabularnewline
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedleft
avg\_reblogs
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_favourites
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_replies
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
engagement\_rate
\end{minipage} \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedleft
avg\_reblogs
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_favourites
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_replies
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_posts
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
engagement\_rate
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
0.07 & 0 & 0.03 & 120 & 0.03 \\
\end{longtable}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Distribution of engagement}
\NormalTok{engagement\_dist }\OtherTok{\textless{}{-}}\NormalTok{ public\_timeline }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{select}\NormalTok{(reblogs\_count, favourites\_count, replies\_count) }\SpecialCharTok{\%\textgreater{}\%}
  \FunctionTok{pivot\_longer}\NormalTok{(}\FunctionTok{everything}\NormalTok{(), }\AttributeTok{names\_to =} \StringTok{"metric"}\NormalTok{, }\AttributeTok{values\_to =} \StringTok{"count"}\NormalTok{)}

\NormalTok{engagement\_box }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(engagement\_dist, }\FunctionTok{aes}\NormalTok{(}\AttributeTok{x =}\NormalTok{ metric, }\AttributeTok{y =}\NormalTok{ count, }\AttributeTok{fill =}\NormalTok{ metric)) }\SpecialCharTok{+}
  \FunctionTok{geom\_boxplot}\NormalTok{(}\AttributeTok{alpha =} \FloatTok{0.7}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Distribution of Engagement Metrics"}\NormalTok{,}
       \AttributeTok{x =} \StringTok{"Engagement Type"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Count"}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{theme\_minimal}\NormalTok{() }\SpecialCharTok{+}
  \FunctionTok{theme}\NormalTok{(}\AttributeTok{legend.position =} \StringTok{"none"}\NormalTok{) }\SpecialCharTok{+}
  \FunctionTok{scale\_x\_discrete}\NormalTok{(}\AttributeTok{labels =} \FunctionTok{c}\NormalTok{(}\StringTok{"Favourites"}\NormalTok{, }\StringTok{"Reblogs"}\NormalTok{, }\StringTok{"Replies"}\NormalTok{))}

\FunctionTok{print}\NormalTok{(engagement\_box)}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/engagement_metrics-1} \end{center}

\section{Specialized Community Analysis
(\#rstats)}\label{specialized-community-analysis-rstats}

\subsection{Text Analysis}\label{text-analysis}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Analyze \#rstats posts content}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{nrow}\NormalTok{(rstats\_posts) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
  \CommentTok{\# Clean and prepare text data}
\NormalTok{  rstats\_text }\OtherTok{\textless{}{-}}\NormalTok{ rstats\_posts }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{select}\NormalTok{(id, content, created\_at, reblogs\_count, favourites\_count) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{mutate}\NormalTok{(}
      \CommentTok{\# Remove HTML tags and URLs}
      \AttributeTok{clean\_content =} \FunctionTok{gsub}\NormalTok{(}\StringTok{"\textless{}.*?\textgreater{}"}\NormalTok{, }\StringTok{""}\NormalTok{, content),}
      \AttributeTok{clean\_content =} \FunctionTok{gsub}\NormalTok{(}\StringTok{"http}\SpecialCharTok{\textbackslash{}\textbackslash{}}\StringTok{S+"}\NormalTok{, }\StringTok{""}\NormalTok{, clean\_content),}
      \AttributeTok{clean\_content =} \FunctionTok{gsub}\NormalTok{(}\StringTok{"}\SpecialCharTok{\textbackslash{}\textbackslash{}}\StringTok{s+"}\NormalTok{, }\StringTok{" "}\NormalTok{, clean\_content),}
      \AttributeTok{clean\_content =} \FunctionTok{tolower}\NormalTok{(clean\_content)}
\NormalTok{    )}

  \CommentTok{\# Tokenize text for word analysis}
\NormalTok{  rstats\_words }\OtherTok{\textless{}{-}}\NormalTok{ rstats\_text }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{unnest\_tokens}\NormalTok{(word, clean\_content) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{anti\_join}\NormalTok{(stop\_words, }\AttributeTok{by =} \StringTok{"word"}\NormalTok{) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{filter}\NormalTok{(}\SpecialCharTok{!}\FunctionTok{grepl}\NormalTok{(}\StringTok{"\^{}}\SpecialCharTok{\textbackslash{}\textbackslash{}}\StringTok{d+$"}\NormalTok{, word)) }\SpecialCharTok{\%\textgreater{}\%}  \CommentTok{\# Remove pure numbers}
    \FunctionTok{filter}\NormalTok{(}\FunctionTok{nchar}\NormalTok{(word) }\SpecialCharTok{\textgreater{}} \DecValTok{2}\NormalTok{)  }\CommentTok{\# Remove very short words}

  \CommentTok{\# Get most common words}
\NormalTok{  top\_words }\OtherTok{\textless{}{-}}\NormalTok{ rstats\_words }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{count}\NormalTok{(word, }\AttributeTok{sort =} \ConstantTok{TRUE}\NormalTok{) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{head}\NormalTok{(}\DecValTok{15}\NormalTok{)}

  \CommentTok{\# Create word frequency plot}
\NormalTok{  word\_plot }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(top\_words, }\FunctionTok{aes}\NormalTok{(}\AttributeTok{x =} \FunctionTok{reorder}\NormalTok{(word, n), }\AttributeTok{y =}\NormalTok{ n)) }\SpecialCharTok{+}
    \FunctionTok{geom\_col}\NormalTok{(}\AttributeTok{fill =} \StringTok{"purple"}\NormalTok{, }\AttributeTok{alpha =} \FloatTok{0.7}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{coord\_flip}\NormalTok{() }\SpecialCharTok{+}
    \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Most Common Words in \#rstats Posts"}\NormalTok{,}
         \AttributeTok{x =} \StringTok{"Word"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Frequency"}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{theme\_minimal}\NormalTok{()}

  \FunctionTok{print}\NormalTok{(word\_plot)}

  \FunctionTok{kable}\NormalTok{(top\_words, }\AttributeTok{caption =} \StringTok{"Top Words in \#rstats Posts"}\NormalTok{)}
\NormalTok{\}}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/text_analysis-1} \end{center}

\begin{longtable}[]{@{}lr@{}}
\caption{Top Words in \#rstats Posts}\tabularnewline
\toprule\noalign{}
word & n \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
word & n \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
rstats & 116 \\
quot & 60 \\
data & 35 \\
package & 30 \\
packages & 19 \\
code & 15 \\
datascience & 15 \\
cran & 12 \\
0.00 & 11 \\
ggplot2 & 11 \\
python & 10 \\
science & 10 \\
time & 10 \\
wordle & 10 \\
analysis & 9 \\
\end{longtable}

\subsection{Comparative Engagement
Analysis}\label{comparative-engagement-analysis}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Compare engagement between different content types}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{nrow}\NormalTok{(public\_timeline) }\SpecialCharTok{\textgreater{}} \DecValTok{0} \SpecialCharTok{\&\&} \FunctionTok{nrow}\NormalTok{(rstats\_posts) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}

  \CommentTok{\# Create comparison dataset}
\NormalTok{  comparison\_data }\OtherTok{\textless{}{-}} \FunctionTok{bind\_rows}\NormalTok{(}
\NormalTok{    public\_timeline }\SpecialCharTok{\%\textgreater{}\%}
      \FunctionTok{select}\NormalTok{(reblogs\_count, favourites\_count, replies\_count) }\SpecialCharTok{\%\textgreater{}\%}
      \FunctionTok{mutate}\NormalTok{(}\AttributeTok{content\_type =} \StringTok{"General Public"}\NormalTok{),}
\NormalTok{    rstats\_posts }\SpecialCharTok{\%\textgreater{}\%}
      \FunctionTok{select}\NormalTok{(reblogs\_count, favourites\_count, replies\_count) }\SpecialCharTok{\%\textgreater{}\%}
      \FunctionTok{mutate}\NormalTok{(}\AttributeTok{content\_type =} \StringTok{"\#rstats"}\NormalTok{)}
\NormalTok{  )}

  \CommentTok{\# Create engagement comparison plot}
\NormalTok{  engagement\_comparison }\OtherTok{\textless{}{-}}\NormalTok{ comparison\_data }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{pivot\_longer}\NormalTok{(}\AttributeTok{cols =} \FunctionTok{c}\NormalTok{(reblogs\_count, favourites\_count, replies\_count),}
                 \AttributeTok{names\_to =} \StringTok{"metric"}\NormalTok{, }\AttributeTok{values\_to =} \StringTok{"count"}\NormalTok{) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{group\_by}\NormalTok{(content\_type, metric) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{summarise}\NormalTok{(}\AttributeTok{avg\_count =} \FunctionTok{mean}\NormalTok{(count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\AttributeTok{.groups =} \StringTok{"drop"}\NormalTok{)}

\NormalTok{  comparison\_plot }\OtherTok{\textless{}{-}} \FunctionTok{ggplot}\NormalTok{(engagement\_comparison,}
                           \FunctionTok{aes}\NormalTok{(}\AttributeTok{x =}\NormalTok{ metric, }\AttributeTok{y =}\NormalTok{ avg\_count, }\AttributeTok{fill =}\NormalTok{ content\_type)) }\SpecialCharTok{+}
    \FunctionTok{geom\_col}\NormalTok{(}\AttributeTok{position =} \StringTok{"dodge"}\NormalTok{, }\AttributeTok{alpha =} \FloatTok{0.7}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{labs}\NormalTok{(}\AttributeTok{title =} \StringTok{"Average Engagement: General vs \#rstats Posts"}\NormalTok{,}
         \AttributeTok{x =} \StringTok{"Engagement Metric"}\NormalTok{, }\AttributeTok{y =} \StringTok{"Average Count"}\NormalTok{,}
         \AttributeTok{fill =} \StringTok{"Content Type"}\NormalTok{) }\SpecialCharTok{+}
    \FunctionTok{theme\_minimal}\NormalTok{() }\SpecialCharTok{+}
    \FunctionTok{theme}\NormalTok{(}\AttributeTok{axis.text.x =} \FunctionTok{element\_text}\NormalTok{(}\AttributeTok{angle =} \DecValTok{45}\NormalTok{, }\AttributeTok{hjust =} \DecValTok{1}\NormalTok{)) }\SpecialCharTok{+}
    \FunctionTok{scale\_x\_discrete}\NormalTok{(}\AttributeTok{labels =} \FunctionTok{c}\NormalTok{(}\StringTok{"Favourites"}\NormalTok{, }\StringTok{"Reblogs"}\NormalTok{, }\StringTok{"Replies"}\NormalTok{))}

  \FunctionTok{print}\NormalTok{(comparison\_plot)}

  \FunctionTok{kable}\NormalTok{(engagement\_comparison, }\AttributeTok{caption =} \StringTok{"Engagement Comparison by Content Type"}\NormalTok{)}

  \CommentTok{\# Statistical summary}
\NormalTok{  comparison\_summary }\OtherTok{\textless{}{-}}\NormalTok{ comparison\_data }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{group\_by}\NormalTok{(content\_type) }\SpecialCharTok{\%\textgreater{}\%}
    \FunctionTok{summarise}\NormalTok{(}
      \AttributeTok{avg\_reblogs =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(reblogs\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
      \AttributeTok{avg\_favourites =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(favourites\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
      \AttributeTok{avg\_replies =} \FunctionTok{round}\NormalTok{(}\FunctionTok{mean}\NormalTok{(replies\_count, }\AttributeTok{na.rm =} \ConstantTok{TRUE}\NormalTok{), }\DecValTok{2}\NormalTok{),}
      \AttributeTok{total\_engagement =}\NormalTok{ avg\_reblogs }\SpecialCharTok{+}\NormalTok{ avg\_favourites }\SpecialCharTok{+}\NormalTok{ avg\_replies,}
      \AttributeTok{.groups =} \StringTok{"drop"}
\NormalTok{    )}

  \FunctionTok{kable}\NormalTok{(comparison\_summary, }\AttributeTok{caption =} \StringTok{"Summary Statistics by Content Type"}\NormalTok{)}
\NormalTok{\}}
\end{Highlighting}
\end{Shaded}

\begin{center}\includegraphics{Social_Media_Assignment_files/figure-latex/comparative_analysis-1} \end{center}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2113}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1690}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2113}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.1690}}
  >{\raggedleft\arraybackslash}p{(\linewidth - 8\tabcolsep) * \real{0.2394}}@{}}
\caption{Summary Statistics by Content Type}\tabularnewline
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
content\_type
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_reblogs
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_favourites
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_replies
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_engagement
\end{minipage} \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
content\_type
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_reblogs
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_favourites
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
avg\_replies
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
total\_engagement
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
\#rstats & 3.41 & 2.51 & 0.54 & 6.46 \\
General Public & 0.07 & 0.00 & 0.03 & 0.10 \\
\end{longtable}

\section{Key Findings and Insights}\label{key-findings-and-insights}

\subsection{Activity Patterns}\label{activity-patterns}

\begin{longtable}[]{@{}ll@{}}
\caption{Key Performance Indicators Summary}\tabularnewline
\toprule\noalign{}
Metric & Value \\
\midrule\noalign{}
\endfirsthead
\toprule\noalign{}
Metric & Value \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
Average Weekly Posts & 897,948.3 posts \\
Average Weekly Logins & 158,212.8 logins \\
Average Weekly Registrations & 13,021.25 registrations \\
Top Hashtag Uses & 817 uses \\
General Post Avg Engagement & 0.1 interactions \\
\#rstats Post Avg Engagement & 6.5 interactions \\
\end{longtable}

\subsection{Strategic Implications}\label{strategic-implications}

Based on the analysis of Mastodon social media data, several key
insights emerge:

\subsubsection{\texorpdfstring{1. \textbf{Decentralized Engagement
Patterns}}{1. Decentralized Engagement Patterns}}\label{decentralized-engagement-patterns}

\begin{itemize}
\tightlist
\item
  Mastodon shows different engagement patterns compared to centralized
  platforms
\item
  Community-specific content (\#rstats) demonstrates higher engagement
  rates
\item
  Technical communities show more focused and meaningful interactions
\end{itemize}

\subsubsection{\texorpdfstring{2. \textbf{Content Strategy
Recommendations}}{2. Content Strategy Recommendations}}\label{content-strategy-recommendations}

\begin{itemize}
\tightlist
\item
  \textbf{Hashtag Optimization}: Focus on trending hashtags like
  \#caturday, \#photography, and \#art
\item
  \textbf{Timing Strategy}: Post during peak hours identified in the
  hourly analysis
\item
  \textbf{Community Engagement}: Technical content receives higher
  engagement, suggesting value in niche expertise
\end{itemize}

\subsubsection{\texorpdfstring{3. \textbf{Platform-Specific
Considerations}}{3. Platform-Specific Considerations}}\label{platform-specific-considerations}

\begin{itemize}
\tightlist
\item
  Decentralized nature allows for more targeted community building
\item
  Instance-specific strategies may be more effective than broad
  approaches
\item
  Quality over quantity appears to drive engagement in specialized
  communities
\end{itemize}

\subsubsection{\texorpdfstring{4. \textbf{Growth
Opportunities}}{4. Growth Opportunities}}\label{growth-opportunities}

\begin{itemize}
\tightlist
\item
  Consistent weekly growth in posts and registrations indicates platform
  expansion
\item
  Technical communities represent high-value engagement opportunities
\item
  Cross-instance communication enables broader reach while maintaining
  community focus
\end{itemize}

\section{Conclusions and
Recommendations}\label{conclusions-and-recommendations}

\subsection{Summary of Findings}\label{summary-of-findings}

This analysis of Mastodon social media data reveals several important
patterns:

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Steady Growth}: The platform shows consistent growth in user
  activity and registrations
\item
  \textbf{Community-Driven Engagement}: Specialized communities
  (\#rstats) show higher engagement rates
\item
  \textbf{Diverse Content Ecosystem}: Trending hashtags span various
  interests from casual (\#caturday) to serious (\#climate)
\item
  \textbf{Time-Based Patterns}: Clear posting patterns emerge throughout
  the day
\end{enumerate}

\subsection{Recommendations for Social Media
Strategy}\label{recommendations-for-social-media-strategy}

\subsubsection{For Organizations}\label{for-organizations}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Invest in Community Building}: Focus on building genuine
  communities around specific interests
\item
  \textbf{Quality Content}: Prioritize high-quality, specialized content
  over broad appeal
\item
  \textbf{Engagement Over Reach}: Focus on meaningful interactions
  rather than follower counts
\item
  \textbf{Instance Strategy}: Consider the benefits of joining or
  creating topic-specific instances
\end{enumerate}

\subsubsection{For Researchers}\label{for-researchers}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Decentralized Analysis}: Develop new metrics for analyzing
  federated social networks
\item
  \textbf{Community Dynamics}: Study how decentralization affects
  community formation and engagement
\item
  \textbf{Cross-Platform Comparison}: Compare engagement patterns
  between centralized and decentralized platforms
\end{enumerate}

\subsection{Limitations and Future
Research}\label{limitations-and-future-research}

\begin{itemize}
\tightlist
\item
  \textbf{Sample Size}: This analysis used limited samples (100 posts
  each category)
\item
  \textbf{Time Period}: Analysis covers a specific time period and may
  not reflect long-term trends
\item
  \textbf{Instance Focus}: Analysis focused on mastodon.social; other
  instances may show different patterns
\item
  \textbf{API Limitations}: Some data points may be limited by API
  access restrictions
\end{itemize}

\subsection{Technical Implementation
Notes}\label{technical-implementation-notes}

This analysis was conducted using the \texttt{rtoot} package for R,
which provides comprehensive access to the Mastodon API. The methodology
can be replicated and extended for:

\begin{itemize}
\tightlist
\item
  Longitudinal studies of platform growth
\item
  Cross-instance comparative analysis
\item
  Real-time monitoring of trending topics
\item
  Community-specific engagement studies
\end{itemize}

\begin{center}\rule{0.5\linewidth}{0.5pt}\end{center}

\textbf{Analysis completed using rtoot package for R} \textbf{Western
Sydney University MBA - Social Media Analytics} \textbf{Date:
2025-05-25}

\end{document}
