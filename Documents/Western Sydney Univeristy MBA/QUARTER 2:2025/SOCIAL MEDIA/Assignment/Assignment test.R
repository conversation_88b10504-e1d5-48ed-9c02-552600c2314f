




install.packages('rtoot')
library(rtoot)


auth_setup()

auth_setup(name = "account1")

token <- readRDS(file.path(tools::R_user_dir("rtoot", "config"), "account1.rds"))
get_status(id = "109297677620300632", instance = "mastodon.social", token = token) 

options("rtoot_token" = file.path(tools::R_user_dir("rtoot", "config"), "account1.rds"))

auth_setup(clipboard = TRUE)


get_instance_general(instance = "mastodon.social") %>% 
  .[str_detect(tolower(.), 'scho')]

get_instance_activity(instance = "mastodon.social")
get_instance_trends(instance = "mastodon.social")


get_timeline_public(instance = "mastodon.social") %>% 
  glimpse()


get_timeline_hashtag(hashtag = "rstats", instance = "mastodon.social") %>% 
  glimpse()





search_accounts("13179")

get_account_followers('13179')

get_account_following('13179')

get_account_statuses('13179')











get_instance_general(instance = "mastodon.social")
get_instance_trends(instance = "mastodon.social")






















































































































































































