# Test script to verify R Markdown works with real data
# This script tests the R Markdown rendering process

# Load required packages
if (!require(rmarkdown)) {
  install.packages("rmarkdown")
}
if (!require(knitr)) {
  install.packages("knitr")
}

library(rmarkdown)
library(knitr)

cat("=== TESTING R MARKDOWN RENDERING ===\n")

# Check if authentication exists
token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")

if (!file.exists(token_path)) {
  cat("✗ Authentication file not found\n")
  cat("Please run: auth_setup(name = 'account1') first\n")
  stop("Authentication required")
} else {
  cat("✓ Authentication file found\n")
}

# Check if R Markdown file exists
if (!file.exists("Social_Media_Assignment.Rmd")) {
  cat("✗ R Markdown file not found\n")
  stop("Social_Media_Assignment.Rmd not found")
} else {
  cat("✓ R Markdown file found\n")
}

# Test HTML rendering first (easier than PDF)
cat("\n=== TESTING HTML RENDERING ===\n")
tryCatch({
  rmarkdown::render("Social_Media_Assignment.Rmd", 
                   output_format = "html_document",
                   output_file = "Social_Media_Assignment_test.html")
  cat("✓ HTML rendering successful!\n")
  cat("  Output file: Social_Media_Assignment_test.html\n")
}, error = function(e) {
  cat("✗ HTML rendering failed:\n")
  cat("  Error:", e$message, "\n")
})

# Test PDF rendering
cat("\n=== TESTING PDF RENDERING ===\n")
tryCatch({
  rmarkdown::render("Social_Media_Assignment.Rmd", 
                   output_format = "pdf_document",
                   output_file = "Social_Media_Assignment_test.pdf")
  cat("✓ PDF rendering successful!\n")
  cat("  Output file: Social_Media_Assignment_test.pdf\n")
}, error = function(e) {
  cat("✗ PDF rendering failed:\n")
  cat("  Error:", e$message, "\n")
  cat("  Note: PDF requires LaTeX. Try installing tinytex:\n")
  cat("  install.packages('tinytex')\n")
  cat("  tinytex::install_tinytex()\n")
})

# List generated files
cat("\n=== GENERATED FILES ===\n")
output_files <- c("Social_Media_Assignment_test.html", 
                 "Social_Media_Assignment_test.pdf",
                 "mastodon_data.RData")

for (file in output_files) {
  if (file.exists(file)) {
    cat("✓", file, "created\n")
    cat("  Size:", file.size(file), "bytes\n")
  }
}

cat("\n=== TEST COMPLETED ===\n")
cat("If HTML rendering worked, you can view the report in your browser.\n")
cat("If PDF rendering worked, you have a complete assignment ready for submission!\n")
