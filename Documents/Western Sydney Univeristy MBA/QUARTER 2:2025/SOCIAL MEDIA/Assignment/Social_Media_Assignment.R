# Social Media Analytics Assignment - Mastodon Data Analysis using rtoot
# Western Sydney University MBA - Quarter 2:2025
# Course: Social Media Analytics

# Install and load required packages
if (!require(rtoot)) {
  install.packages("rtoot")
}
if (!require(dplyr)) {
  install.packages("dplyr")
}
if (!require(ggplot2)) {
  install.packages("ggplot2")
}
if (!require(lubridate)) {
  install.packages("lubridate")
}
if (!require(tidytext)) {
  install.packages("tidytext")
}
if (!require(wordcloud)) {
  install.packages("wordcloud")
}

library(rtoot)
library(dplyr)
library(ggplot2)
library(lubridate)
library(tidytext)
library(wordcloud)

# ============================================================================
# PART 1: AUTHENTICATION AND SETUP
# ============================================================================

# Set up authentication for Mastodon
# Note: This creates a token for accessing Mastodon's API
auth_setup(name = "account1")

# Load the authentication token
token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")
token <- readRDS(token_path)

# Set default token for the session
options("rtoot_token" = token_path)

# ============================================================================
# PART 2: INSTANCE ANALYSIS
# ============================================================================

# Get general information about mastodon.social instance
instance_info <- get_instance_general(instance = "mastodon.social")
print("=== MASTODON.SOCIAL INSTANCE INFORMATION ===")
print(instance_info)

# Get instance activity data (last 3 months)
instance_activity <- get_instance_activity(instance = "mastodon.social")
print("=== INSTANCE ACTIVITY (Last 3 Months) ===")
print(instance_activity)

# Get trending hashtags
instance_trends <- get_instance_trends(instance = "mastodon.social")
print("=== TRENDING HASHTAGS ===")
print(head(instance_trends, 10))

# ============================================================================
# PART 3: CONTENT ANALYSIS
# ============================================================================

# Get public timeline data
public_timeline <- get_timeline_public(instance = "mastodon.social", 
                                      limit = 100)
print("=== PUBLIC TIMELINE SAMPLE ===")
print(paste("Retrieved", nrow(public_timeline), "posts from public timeline"))

# Get posts with specific hashtag (#rstats)
rstats_posts <- get_timeline_hashtag(hashtag = "rstats", 
                                    instance = "mastodon.social", 
                                    limit = 100)
print("=== #RSTATS HASHTAG ANALYSIS ===")
print(paste("Retrieved", nrow(rstats_posts), "posts with #rstats hashtag"))

# Get a specific status for detailed analysis
sample_status <- get_status(id = "109297677620300632",
                           instance = "mastodon.social",
                           token = token)
print("=== SAMPLE STATUS ANALYSIS ===")
print(sample_status)

# ============================================================================
# PART 4: DATA ANALYSIS AND VISUALIZATION
# ============================================================================

# Analyze instance activity trends
if (nrow(instance_activity) > 0) {
  # Convert week to proper date format
  instance_activity$week <- as.Date(instance_activity$week)
  
  # Create activity trend plot
  activity_plot <- ggplot(instance_activity, aes(x = week)) +
    geom_line(aes(y = statuses, color = "Posts"), size = 1.2) +
    geom_line(aes(y = logins, color = "Logins"), size = 1.2) +
    geom_line(aes(y = registrations * 10, color = "Registrations (x10)"),
              size = 1.2) +
    labs(title = "Mastodon.social Instance Activity Trends",
         subtitle = "Weekly activity over the last 3 months",
         x = "Week", y = "Count",
         color = "Metric") +
    theme_minimal() +
    theme(legend.position = "bottom")
  
  print(activity_plot)
  
  # Summary statistics
  cat("\n=== ACTIVITY SUMMARY STATISTICS ===\n")
  cat("Average weekly posts:", round(mean(instance_activity$statuses)), "\n")
  cat("Average weekly logins:", round(mean(instance_activity$logins)), "\n")
  cat("Average weekly registrations:",
      round(mean(instance_activity$registrations)), "\n")
}

# Analyze trending hashtags
if (nrow(instance_trends) > 0) {
  # Get top 10 trending hashtags
  top_trends <- instance_trends %>%
    group_by(name) %>%
    summarise(total_uses = sum(uses, na.rm = TRUE),
              total_accounts = sum(accounts, na.rm = TRUE)) %>%
    arrange(desc(total_uses)) %>%
    head(10)
  
  # Create hashtag popularity plot
  trends_plot <- ggplot(top_trends, aes(x = reorder(name, total_uses),
                                       y = total_uses)) +
    geom_col(fill = "steelblue", alpha = 0.7) +
    coord_flip() +
    labs(title = "Top 10 Trending Hashtags on Mastodon.social",
         x = "Hashtag", y = "Total Uses") +
    theme_minimal()
  
  print(trends_plot)
  
  cat("\n=== TOP TRENDING HASHTAGS ===\n")
  print(top_trends)
}

# Analyze public timeline content
if (nrow(public_timeline) > 0) {
  # Convert created_at to proper datetime
  public_timeline$created_at <- as.POSIXct(public_timeline$created_at)
  
  # Analyze posting patterns by hour
  public_timeline$hour <- hour(public_timeline$created_at)
  
  hourly_posts <- public_timeline %>%
    count(hour) %>%
    arrange(hour)
  
  # Create hourly posting pattern plot
  hourly_plot <- ggplot(hourly_posts, aes(x = hour, y = n)) +
    geom_col(fill = "darkgreen", alpha = 0.7) +
    labs(title = "Posting Patterns by Hour of Day",
         subtitle = "Based on public timeline sample",
         x = "Hour of Day", y = "Number of Posts") +
    scale_x_continuous(breaks = 0:23) +
    theme_minimal()
  
  print(hourly_plot)
  
  # Analyze engagement metrics
  engagement_summary <- public_timeline %>%
    summarise(
      avg_reblogs = round(mean(reblogs_count, na.rm = TRUE), 2),
      avg_favourites = round(mean(favourites_count, na.rm = TRUE), 2),
      avg_replies = round(mean(replies_count, na.rm = TRUE), 2),
      total_posts = n()
    )
  
  cat("\n=== ENGAGEMENT METRICS SUMMARY ===\n")
  print(engagement_summary)
}

# ============================================================================
# PART 5: TEXT ANALYSIS AND SENTIMENT
# ============================================================================

# Analyze #rstats posts content
if (nrow(rstats_posts) > 0) {
  # Clean and prepare text data
  rstats_text <- rstats_posts %>%
    select(id, content, created_at, reblogs_count, favourites_count) %>%
    mutate(
      # Remove HTML tags
      clean_content = gsub("<.*?>", "", content),
      # Remove URLs
      clean_content = gsub("http\\S+", "", clean_content),
      # Remove extra whitespace
      clean_content = gsub("\\s+", " ", clean_content),
      # Convert to lowercase
      clean_content = tolower(clean_content)
    )
  
  # Tokenize text for word analysis
  rstats_words <- rstats_text %>%
    unnest_tokens(word, clean_content) %>%
    anti_join(stop_words, by = "word") %>%
    filter(!grepl("^\\d+$", word)) %>%  # Remove pure numbers
    filter(nchar(word) > 2)  # Remove very short words
  
  # Get most common words
  top_words <- rstats_words %>%
    count(word, sort = TRUE) %>%
    head(20)
  
  # Create word frequency plot
  word_plot <- ggplot(top_words, aes(x = reorder(word, n), y = n)) +
    geom_col(fill = "purple", alpha = 0.7) +
    coord_flip() +
    labs(title = "Most Common Words in #rstats Posts",
         x = "Word", y = "Frequency") +
    theme_minimal()
  
  print(word_plot)
  
  cat("\n=== TOP WORDS IN #RSTATS POSTS ===\n")
  print(top_words)
  
  # Create word cloud if we have enough words
  if (nrow(top_words) >= 10) {
    wordcloud(words = top_words$word,
              freq = top_words$n,
              min.freq = 2,
              max.words = 50,
              random.order = FALSE,
              colors = brewer.pal(8, "Dark2"))
  }
}

# ============================================================================
# PART 6: COMPARATIVE ANALYSIS
# ============================================================================

# Compare engagement between different content types
if (nrow(public_timeline) > 0 && nrow(rstats_posts) > 0) {
  
  # Create comparison dataset
  comparison_data <- bind_rows(
    public_timeline %>%
      select(reblogs_count, favourites_count, replies_count) %>%
      mutate(content_type = "General Public"),
    rstats_posts %>%
      select(reblogs_count, favourites_count, replies_count) %>%
      mutate(content_type = "#rstats")
  )
  
  # Create engagement comparison plot
  engagement_comparison <- comparison_data %>%
    pivot_longer(cols = c(reblogs_count, favourites_count, replies_count),
                 names_to = "metric", values_to = "count") %>%
    group_by(content_type, metric) %>%
    summarise(avg_count = mean(count, na.rm = TRUE), .groups = "drop")
  
  comparison_plot <- ggplot(engagement_comparison,
                           aes(x = metric, y = avg_count, 
                               fill = content_type)) +
    geom_col(position = "dodge", alpha = 0.7) +
    labs(title = "Average Engagement: General vs #rstats Posts",
         x = "Engagement Metric", y = "Average Count",
         fill = "Content Type") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  print(comparison_plot)
  
  cat("\n=== ENGAGEMENT COMPARISON ===\n")
  print(engagement_comparison)
}

# ============================================================================
# PART 7: CONCLUSIONS AND INSIGHTS
# ============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("SOCIAL MEDIA ANALYTICS SUMMARY - MASTODON ANALYSIS\n")
cat(paste(rep("=", 60), collapse = ""), "\n\n")

cat("1. INSTANCE OVERVIEW:\n")
if (exists("instance_info")) {
  cat("   - Instance: mastodon.social\n")
  cat("   - Analysis Date:", format(Sys.Date(), "%Y-%m-%d"), "\n")
}

cat("\n2. ACTIVITY PATTERNS:\n")
if (exists("instance_activity") && nrow(instance_activity) > 0) {
  cat("   - Average weekly posts:",
      round(mean(instance_activity$statuses)), "\n")
  cat("   - Average weekly logins:",
      round(mean(instance_activity$logins)), "\n")
}

cat("\n3. CONTENT ANALYSIS:\n")
if (exists("public_timeline") && nrow(public_timeline) > 0) {
  cat("   - Public timeline posts analyzed:", nrow(public_timeline), "\n")
}
if (exists("rstats_posts") && nrow(rstats_posts) > 0) {
  cat("   - #rstats posts analyzed:", nrow(rstats_posts), "\n")
}

cat("\n4. KEY INSIGHTS:\n")
cat("   - Mastodon shows decentralized social media engagement patterns\n")
cat("   - Technical communities (#rstats) show specific behaviors\n")
cat("   - Posting patterns vary by time of day and content type\n")

cat("\nAnalysis completed using rtoot package for R\n")
cat("Western Sydney University MBA - Social Media Analytics\n")
